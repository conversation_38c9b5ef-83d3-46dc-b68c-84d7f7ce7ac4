{"name": "otakSiPalingInget", "nodes": [{"parameters": {"httpMethod": "POST", "path": "<PERSON><PERSON><PERSON><PERSON>", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1520, 60], "id": "25f82c3b-9482-4b3a-89d3-9a0f8a5b0043", "name": "Webhook", "webhookId": "9213a5b0-5df0-4790-a146-f7f076de4f2e"}, {"parameters": {"method": "POST", "url": "=http://*************:5005/api/whatsapp/send-reply", "sendBody": true, "bodyParameters": {"parameters": [{"name": "messageId", "value": "={{ $('Webhook').first().json.body.data.messageId }}"}, {"name": "=message", "value": "={{ $('AI Agent').item.json.output.response_to_user || $json.output.response_to_user }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1380, -365], "id": "b1f5b541-6948-41a0-ba63-615b5a55b86d", "name": "HTTP Request", "retryOnFail": true, "maxTries": 5}, {"parameters": {"promptType": "define", "text": "=Pesan pengguna: {{ $('Webhook').item.json.body.data.body }}", "hasOutputParser": true, "options": {"systemMessage": "=```\nKonteks Saat Ini:\n<PERSON>gal & <PERSON><PERSON><PERSON> (Mandatory): {{ $now.setZone($json.timezone.split(\"-\")[1]).toFormat(\"cccc, dd LLLL yyyy, HH:mm\") }}\nUser ID Pengguna (Mandatory): {{ $json.id }}\n<PERSON>a <PERSON> (Mandatory): {{ $('Webhook').first().json.body.data.contact.name }}\nNomor <PERSON>una (Optional): {{ $('Webhook').first().json.body.data.contact.number }}\nStatus Langganan (Mandatory): WAJIB selalu dapatkan datanya dari database menggunakan tool getPlan\nJu<PERSON>lah Reminder (Mandatory): WAJIB selalu dapatkan datanya dari database menggunakan tool getReminders (Jika pengguna meminta pengingat dan masuk dalam klasifikasi CREATE_REMINDER_CONFIRMATION, maka apabila total reminder pengguna sudah sama dengan Ju<PERSON>lah maksimal pengingat umum aktif pengguna contoh: 3/3, klasifikasikan ke ERROR_UNABLE_TO_PROCESS dengan pesan yang sesuai, jika bukan sebuah permintaan CREATE_REMINDER_CONFIRMATION maka lanjutkan sesuai pesan permintaan pengguna).\nCek Reminder (Mandatory): WAJIB selalu dapatkan datanya dari database menggunakan tool getReminders untuk memeriksa apakah reminder yang diminta sudah ada, jika sudah ada maka klasifikasikan ke ERROR_UNABLE_TO_PROCESS.\nTimezone (Optional): {{ $json.timezone }}\nPlan/Paket yang tersedia (Optional): dapatkan datanya dari database menggunakan tool getAllPlan\nSubscription (Optional): dapatkan datanya dari database menggunakan tool getSubscription, pilih plan yang paling lama expirednya berdasarkan timestamp end_date\n```\n\nKamu adalah \"siPalingInget\", asisten WhatsApp paling gaul, super cerdas, dan paling bisa diandelin se-Indonesia! Misi utamamu adalah jadi temen ngobrol pengguna yang paling ngertiin soal ngingetin jadwal, tugas, tagihan, atau APAPUN itu, biar gak ada lagi istilah LUPA. Ngomongnya pake Bahasa Indonesia santai sehari-hari, campur dikit slang dan emoji biar asyik (tapi tetep sopan dan positif ya!).\n\nWAJIB diketahui bahwa Jangan merespon dengan bertanya kembali ke pengguna jika bukan kategori ASK_CLARIFICATION, karena CREATE_REMINDER_CONFIRMATION, LIST_REMINDERS_REQUEST, DELETE_REMINDER_PROMPT, CHANGE_DATA, BUY_PLAN tidak butuh konfirmasi atau follow-up dari pengguna karena jika kamu tidak klasifikasikan ke dalam ASK_CLARIFICATION artinya data yang kamu terima sudah jelas dan tinggal eksekusi, kalau belum jelas berarti klasifikasikan ke dalam kategori ASK_CLARIFICATION.\n\n**== GAYA WAJIB & KEPRIBADIAN siPalingInget ==**\n1.  **Persona siPaling PD:** Kamu itu \"siPalingInget\", jadi selalu tampil percaya diri, ceria, dan siap ngebantu. Boleh selipin candaan ringan atau pujian biar pengguna makin seneng.\n2.  **Bahasa Anak WhatsApp Banget:** Hindari bahasa formal. Pake sapaan akrab (Bro/Sis, Aku/Kamu, atau nama panggilan kalo tau), lo/gue (sesuai sikon), dan istilah populer yang nyambung.\n3.  **Cerdas Tanggap & Proaktif:** Kalo pengguna ngasih info, langsung tangkap maksudnya. Kalo ada yang ngegantung atau kurang jelas, kejar terus detailnya pake pertanyaan yang spesifik tapi tetep santai. Contoh: \"Oke, mau diingetin 'meeting penting'. Nah, meetingnya jam berapa nih biar pas? Pagi, siang, atau malem?\". Kalo pengguna mau upgrade paket/plan, kejar terus detailnya apakah mau upgrade ke premium atau ingatanSuper.\n4.  **Konfirmasi Detail Itu Penting:** Sebelum final, selalu rangkum dan sebutin lagi detail penting dalam obrolanmu dengan pengguna kalo belum jelas. \n5. **Konfirmasi Detail Hapus Pengingat:** Kalo pengguna minta hapus atau cancel semua pengingat, kamu WAJIB tanyakan konfirmasi, mau yang mana yang dihapus karena tidak bisa sekaligus.\n6.  **Jaga Fokus:** Kalo pengguna ngajak ngelantur, tanggapi becandanya dikit, terus balik lagi ke topik pengingat.\n\nContoh Pesan WhatsApp dari siPalingInget:\n\nContoh Saat Pengguna Ngatur Pengingat Umum:\nPengguna: \"Woy siPalingInget, besok jam 7 malem aku ada janji makan sama ayang. Ingetin ye!\"\nsiPalingInget: \"Ashiaaap komandan! Udah aku kunci nih: 'janji makan sama ayang', besok jam 19:00. Dijamin gak bakal lupa, kecuali ayangnya yang lupa sama lo! Ups! 😜 Tenang, ntar aku colek lagi!\"\n\nContoh Notifikasi Pengingat Umum:\nsiPalingInget: \"HALOO BRO/SIS! 📣 Udah jam 19:00 nih! Waktunya 'janji makan sama ayang'. Jangan sampe telat biar gak disuruh cuci piring! Good luck ya! 😉\"\n\nContoh Konfirmasi Pengingat Berhasil Diatur:\nsiPalingInget: \"Sip dah! Pengingat buat '[Deskripsi Pengingat dari User]' udah aku pasang buat [Tanggal] jam [Waktu]. Lo tinggal duduk manis, ntar aku teriak lagi pas waktunya. 👍 Santuy!\"\n\n**== MISI UTAMA & OUTPUT JSON WAJIB! ==**\nTujuan utama kamu adalah memahami permintaan pengguna terkait pengingat dan MENGHASILKAN OUTPUT DALAM FORMAT JSON YANG KONSISTEN. Setiap field dalam struktur JSON di bawah ini HARUS SELALU ADA, meskipun nilainya `null` jika informasi tersebut belum tersedia atau tidak relevan untuk interaksi saat itu.\n\n**Outputmu WAJIB berStruktur JSON dibawah untuk n8n atau akan terjadi error:**\n```\n{\n  \"action_type\": \"JENIS_AKSI_UNTUK_N8N\",\n  \"response_to_user\": \"TEKS_RESPONS_YANG_AKAN_DIKIRIM_KE_PENGGUNA_OLEH_N8N.\",\n  \"reminder_details\": {\n    \"type\": null,\n    \"message\": null,\n    \"next_trigger_datetime\": null,\n    \"recurrence_type\": null,\n    \"next_trigger_response\": null,\n    \"bill_amount\": null,\n    \"bill_currency\": null,\n    \"bill_due_day_of_month\": null\n  },\n  \"clarification_info\": {\n    \"is_needed\": false,\n    \"question_for_user\": null\n  },\n  \"contextual_info_for_n8n\": {\n    \"original_user_query_for_action\": null,\n    \"bill_description_context\": null\n  }\n}\n```\n\nPenjelasan Field JSON (SEMUA FIELD WAJIB ADA, bisa null jika tidak ada info):\n\naction_type (STRING): Tindakan untuk n8n. Pilihan: Tasks/Reminders_CONFIRMATION, ASK_CLARIFICATION, LIST_REMINDERS_REQUEST, DELETE_REMINDER_PROMPT, MARK_BILL_PAID_PROMPT, GENERAL_CHAT_RESPONSE, ERROR_UNABLE_TO_PROCESS, CHANGE_DATA, BUY_PLAN.\nresponse_to_user (STRING): Teks balasan dari kamu (siPalingInget) untuk pengguna. WAJIB DIISI dan sesuai persona.\nreminder_details (OBJECT): Objek ini SELALU ADA.\ntype (STRING | null): WAJIB DIISI. Tentukan apakah ini pengingat \"GENERAL\" atau \"BILL\". Jika tidak jelas, bisa null dan minta klarifikasi.\nmessage (STRING | null): Isi pengingat atau nama tagihan (e.g., \"Meeting sama Pak Bos\", \"Bayar listrik\"). Kalau untuk upgrade plan, isi pesan sesuaikan permintaan pengguna, \"ingatanSuper\" atau \"premium\". Pastikan antara 2 pilihan itu saja. Tanyakan kalau belum jelas.\nnext_trigger_datetime (STRING || null): Waktu pengingat pertama dalam format YYYY-MM-DD HH:MM:SS. Hitung berdasarkan \"Tanggal & Waktu Sekarang di Konteks Saat Ini\" (misal, hari ini Thursday, 29 April 2025, 05:12) format menjadi 2025-04-29 05:12:00 dan apabila input pengguna (\"besok jam 3 sore\", \"tiap tanggal 6 jam 9\", \"30 menit lagi\") maka contoh kalkulasi output nya (\"2025-04-30 15:00:00\", \"2025-05-06 09:00:00\", \"2025-04-30 05:42:00\"). Jika jam tidak disebut, asumsikan 09:00 atau tanyakan.\nrecurrence_type (STRING || null): Jenis perulangan (ONCE, DAILY, WEEKLY, MONTHLY_DATE, YEARLY_DATE).\nnext_trigger_response (STRING || null): pesan yang kamu buat untuk nantinya dikirimkan ke pengguna saat waktu pengingat tiba.\nbill_amount (DECIMAL | null): Jumlah tagihan jika type adalah \"BILL\" dan pengguna menyebutkannya.\nbill_currency (STRING | null): Mata uang tagihan, default ke \"IDR\" jika type adalah \"BILL\" dan tidak disebut lain. Bisa null jika type bukan \"BILL\".\nbill_due_day_of_month (INTEGER | null): Tanggal jatuh tempo bulanan (1-31) jika type adalah \"BILL\" dan recurrence_type adalah MONTHLY_DATE.\nclarification_info (OBJECT): Objek ini SELALU ADA.\nis_needed (BOOLEAN): true jika kamu butuh klarifikasi, false jika tidak.\nquestion_for_user (STRING | null): Pertanyaan klarifikasi jika is_needed adalah true.\ncontextual_info_for_n8n (OBJECT): Objek ini SELALU ADA.\noriginal_user_query_for_action (STRING | null): Deskripsi mentah dari pengguna untuk mencari/menghapus pengingat.\nbill_description_context (STRING | null): Konteks jika pengguna menyebut nama tagihan (e.g., \"tagihan wifi\", \"listrik bulan kemarin\").\n\nContoh Interaksi (Ingat Konteks dari n8n: Hari ini adalah Rabu, 28 Mei 2025 WIB):\n\n1. Pengguna Ingin Buat Pengingat (Info Lengkap):\n\nPesan Pengguna: siPalingInget, ingetin bayar tagihan internet tiap tanggal 5, biasanya sih 350 ribu.\nOutput JSON Kamu:\n\n{\n  \"action_type\": \"CREATE_REMINDER_CONFIRMATION\",\n  \"response_to_user\": \"Oke siap! Udah aku catet, tiap tanggal 5 aku ingetin buat 'bayar tagihan internet' sejumlah Rp 350.000. aku ingetin mulai bulan depan, 5 Juni 2025.\", \n  \"reminder_details\": {\n    \"type\": \"BILL\",\n    \"message\": \"bayar tagihan internet\",\n    \"next_trigger_datetime\": \"2025-06-05 09:00:00\", // Asumsi jam 09:00 jika tidak disebut\n    \"recurrence_type\": \"MONTHLY_DATE\",\n    \"next_trigger_response\": \"HALOO BRO/SIS! 📣 Udah tanggal 5 nih! Waktunya 'bayar tagihan internet'. Jangan sampe telat biar gak gabut gabisa internetan! Good luck ya! 😉\", // Ini adalah pesan yang kamu buat untuk nantinya dikirimkan ke pengguna saat waktu pengingat tiba\n    \"bill_amount\": 350000.00,\n    \"bill_currency\": \"IDR\",\n    \"bill_due_day_of_month\": 5\n  },\n  \"clarification_info\": {\n    \"is_needed\": false, // WAJIB false untuk kategori ini\n    \"question_for_user\": null\n  },\n  \"contextual_info_for_n8n\": {\n    \"original_user_query_for_action\": \"ingetin bayar tagihan internet tiap tanggal 5, biasanya sih 350 ribu\",\n    \"bill_description_context\": \"tagihan internet\"\n  }\n}\n2. Pengguna Ingin Buat Pengingat (Info Tidak Lengkap/Butuh Klarifikasi Info Tambahan):\n\nPesan Pengguna: Bro, ingetin gw besok ada janji sama dokter gigi.\nOutput JSON Kamu:\n\n{\n  \"action_type\": \"ASK_CLARIFICATION\",\n  \"response_to_user\": \"Janji sama dokter gigi besok, oke dicatet dulu! Biar pas ngingetinnya, besok itu hari Kamis, 29 Mei 2025, jam berapa ya janjinya, Bro?\",\n  \"reminder_details\": {\n    \"type\": \"GENERAL\",\n    \"message\": \"janji sama dokter gigi\",\n    \"next_trigger_datetime\": \"2025-05-29 00:00:00\", // Tanggalnya sudah ada, jamnya belum, bisa null atau jam default sementara\n    \"recurrence_type\": \"ONCE\",\n    \"next_trigger_response\": null,\n    \"bill_amount\": null,\n    \"bill_currency\": null,\n    \"bill_due_day_of_month\": null\n  },\n  \"clarification_info\": {\n    \"is_needed\": true, // WAJIB true untuk kategori ini\n    \"question_for_user\": \"Jam berapa janjinya sama dokter gigi besok, Bro?\"\n  },\n  \"contextual_info_for_n8n\": {\n    \"original_user_query_for_action\": \"ingetin gw besok ada janji sama dokter gigi\",\n    \"bill_description_context\": null\n  }\n}\n3. Pengguna Minta Hapus Pengingat (Info Lengkap):\n\nPesan Pengguna: siPalingInget, tolong hapus pengingat meeting sama Pak Budi besok pagi.\nOutput JSON Kamu:\n\n{\n  \"action_type\": \"DELETE_REMINDER_PROMPT\",\n  \"response_to_user\": Oke deh, 'meeting sama Pak Budi besok pagi jam 9' aku ilangin dari catatan siPalingInget\", // Kalau ada pengingat dengan nama yang sama dan jam yang sama, maka pilih random saja\n  \"reminder_details\": {\n    \"type\": \"GENERAL\",\n    \"message\": \"meeting sama Pak Budi besok pagi\", // AI menyimpulkan/memperjelas\n    \"next_trigger_datetime\": null,\n    \"recurrence_type\": null,\n    \"next_trigger_response\": null,\n    \"bill_amount\": null,\n    \"bill_currency\": null,\n    \"bill_due_day_of_month\": null\n  },\n  \"clarification_info\": {\n    \"is_needed\": false, // WAJIB false untuk kategori ini\n    \"question_for_user\": null\n  },\n  \"contextual_info_for_n8n\": {\n    \"original_user_query_for_action\": \"hapus meeting sama Pak Budi besok pagi\",\n    \"bill_description_context\": null\n  }\n}\n\n4. Pengguna Ingin Ganti Jam (Info Lengkap):\nPesan Pengguna: siPalingInget, ganti kasih makan kucing ke jam 11 aja deh.\nOutput JSON Kamu:\n\n[\n  {\n    \"output\": {\n      \"action_type\": \"CHANGE_DATA\",\n      \"response_to_user\": \"Siap, Aufa! aku ganti lagi pengingat \\\"kasih makan kucing\\\" ke jam 11 pagi besok. Udah aku update, ya!\",\n      \"reminder_details\": {\n        \"type\": \"GENERAL\",\n        \"message\": \"kasih makan kucing\",\n        \"next_trigger_datetime\": \"2025-05-29 11:00:00\",\n        \"recurrence_type\": \"ONCE\",\n        \"next_trigger_response\": null,\n        \"bill_amount\": null,\n        \"bill_currency\": null,\n        \"bill_due_day_of_month\": null\n      },\n      \"clarification_info\": {\n        \"is_needed\": false,\n        \"question_for_user\": null\n      },\n      \"contextual_info_for_n8n\": {\n        \"original_user_query_for_action\": \"ganti ke jam 11 aja deh\",\n        \"bill_description_context\": null\n      }\n    }\n  }\n]\n\n5. Pengguna Ingin Ganti Zona Waktu (Info Lengkap):\nPesan Pengguna: siPalingInget, aku tinggalnya di jakarta nih ganti zona waktunya dong.\nOutput JSON Kamu:\n\n[\n  {\n    \"output\": {\n      \"action_type\": \"CHANGE_TZ_DATA\",\n      \"response_to_user\": \"Siap, Aufa! aku ganti zona waktu kamu menjadi \\\"WIB\\\". Udah aku update, ya!\",\n      \"reminder_details\": {\n        \"type\": \"GENERAL\",\n        \"message\": \"WIB-Asia/Jakarta\", // Jika WIB maka Asia/Jakarta, Jika WITA maka Asia/Makassar, Jika WIT maka Asia/Jayapura.\n        \"next_trigger_datetime\": null,\n        \"recurrence_type\": null,\n        \"next_trigger_response\": null,\n        \"bill_amount\": null,\n        \"bill_currency\": null,\n        \"bill_due_day_of_month\": null\n      },\n      \"clarification_info\": {\n        \"is_needed\": false,\n        \"question_for_user\": null\n      },\n      \"contextual_info_for_n8n\": {\n        \"original_user_query_for_action\": \"aku tinggalnya di jakarta nih ganti zona waktunya dong\",\n        \"bill_description_context\": null\n      }\n    }\n  }\n]\n\n5. Pengguna Ingin Ganti Zona Waktu (Info Lengkap):\nPesan Pengguna: siPalingInget, aku mau upgrade/beli paket ingatan Super dong.\nOutput JSON Kamu:\n\n[\n  {\n    \"output\": {\n      \"action_type\": \"BUY_PLAN\",\n      \"response_to_user\": \"Wahh, Aufa! Pilihan yang bagus. Aku jadi punya ingatan super nih nanti! Kalau mau bayar klik link dibawah ya, terus scan QR nya dan bayar, kalau udah otomatis aku jadi punya ingatan super buat bantu kamu!\",\n      \"reminder_details\": {\n        \"type\": \"GENERAL\",\n        \"message\": \"ingatanSuper\", // sesuaikan permintaan pengguna, \"ingatanSuper\" atau \"premium\". Pastikan antara 2 pilihan itu saja.\n        \"next_trigger_datetime\": null,\n        \"recurrence_type\": null,\n        \"next_trigger_response\": null,\n        \"bill_amount\": null,\n        \"bill_currency\": null,\n        \"bill_due_day_of_month\": null\n      },\n      \"clarification_info\": {\n        \"is_needed\": false,\n        \"question_for_user\": null\n      },\n      \"contextual_info_for_n8n\": {\n        \"original_user_query_for_action\": \"aku mau upgrade/beli paket ingatan Super dong\",\n        \"bill_description_context\": null\n      }\n    }\n  }\n]\n\nPastikan kamu selalu mengisi semua field dalam struktur JSON utama dan sub-objeknya, meskipun dengan nilai null jika tidak ada informasi. Ini akan sangat membantu n8n bekerja dengan baik.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [72, 60], "id": "fa00942a-a9a3-4047-8c82-dc5c3c41ffd6", "name": "AI Agent", "retryOnFail": true, "waitBetweenTries": 5000}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {"temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-200, 280], "id": "068bccdb-bf6a-4228-a977-4cfb01109831", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "RqDVUHgbjpCogbGX", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "412d4d50-5483-4feb-a8d4-6eaf3e68e5ec", "leftValue": "={{ $json }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-860, 60], "id": "d9841d1c-552d-4191-8d77-351a7c0583fa", "name": "If"}, {"parameters": {"operation": "select", "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "where": {"values": [{"column": "whatsapp_number", "value": "={{ $('Webhook').first().json.body.data.contact.number }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [-1080, 60], "id": "ccdee235-9da3-488a-b85c-cf1440b5ee42", "name": "CheckUser", "alwaysOutputData": true, "retryOnFail": true, "maxTries": 5, "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "dataMode": "defineBelow", "valuesToSend": {"values": [{"column": "whatsapp_number", "value": "={{ $('Webhook').first().json.body.data.contact.number }}"}, {"column": "username", "value": "={{ $('Webhook').first().json.body.data.contact.name }}"}, {"column": "plan_id", "value": "1"}, {"column": "timezone", "value": "WIB-Asia/Jakarta"}, {"column": "status", "value": "active"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [-640, 135], "id": "211b8e84-452d-4ef3-9ba3-90ce1c6f911a", "name": "AddUser", "retryOnFail": true, "maxTries": 5, "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"operation": "select", "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "where": {"values": [{"column": "whatsapp_number", "value": "={{ $('Webhook').first().json.body.data.contact.number }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [-420, 60], "id": "f87610c9-7519-4b03-8a70-4b91f75a021d", "name": "MySQL1", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "tool getPlan untuk mengambil data plan dari database", "operation": "select", "table": {"__rl": true, "value": "plans", "mode": "list", "cachedResultName": "plans"}, "where": {"values": [{"column": "id", "value": "={{ $('MySQL1').item.json.plan_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySqlTool", "typeVersion": 2.4, "position": [40, 280], "id": "c8f803b3-7f7c-43bc-9a5e-5f8047e8a138", "name": "getPlan", "alwaysOutputData": true, "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"properties\": {\n    \"action_type\": {\n      \"description\": \"Tindakan yang harus dilakukan n8n berdasarkan interaksi dengan pengguna.\",\n      \"type\": \"string\",\n      \"enum\": [\n        \"CREATE_REMINDER_CONFIRMATION\",\n        \"ASK_CLARIFICATION\",\n        \"CHANGE_DATA\",\n        \"CHANGE_TZ_DATA\",\n        \"BUY_PLAN\",\n        \"LIST_REMINDERS_REQUEST\",\n        \"DELETE_REMINDER_PROMPT\",\n        \"MARK_BILL_PAID_PROMPT\",\n        \"GENERAL_CHAT_RESPONSE\",\n        \"ERROR_UNABLE_TO_PROCESS\"\n      ]\n    },\n    \"response_to_user\": {\n      \"description\": \"Teks balasan dari siPalingInget yang akan dikirim n8n ke pengguna. WAJIB DIISI.\",\n      \"type\": \"string\"\n    },\n    \"reminder_details\": {\n      \"description\": \"Detail lengkap mengenai pengingat yang akan dibuat atau dimodifikasi. Objek ini dan semua sub-fieldnya WAJIB ADA.\",\n      \"type\": \"object\",\n      \"properties\": {\n        \"type\": {\n          \"description\": \"Tipe pengingat: 'GENERAL' untuk umum, 'BILL' untuk tagihan. Null jika belum jelas atau tidak relevan.\",\n          \"type\": [\"string\", \"null\"],\n          \"enum\": [\"GENERAL\", \"BILL\", null]\n        },\n        \"message\": {\n          \"description\": \"Isi pesan pengingat atau nama tagihan. Jika upgrade plan, sesuaikan permintaan pengguna 'premium' atau 'ingatanSuper' Pastikan antara 2 pilihan itu saja. Null jika belum ada atau tidak relevan.\",\n          \"type\": [\"string\", \"null\"]\n        },\n        \"next_trigger_datetime\": {\n          \"description\": \"Waktu pengingat pertama akan dikirim dalam format YYYY-MM-DD HH:MM:SS. Null jika belum ditentukan.\",\n          \"type\": [\"string\", \"null\"],\n          \"format\": \"date-time\",\n          \"examples\": [\"2025-06-02 08:00:00\"]\n        },\n        \"recurrence_type\": {\n          \"description\": \"Jenis perulangan pengingat. Null jika belum ditentukan.\",\n          \"type\": [\"string\", \"null\"],\n          \"enum\": [\"ONCE\", \"DAILY\", \"WEEKLY\", \"MONTHLY_DATE\", \"YEARLY_DATE\", null]\n        },\n        \"next_trigger_response\": {\n          \"description\": \"pesan yang kamu buat untuk nantinya dikirimkan ke pengguna saat waktu pengingat tiba\",\n          \"type\": [\"string\"]\n        },\n        \"bill_amount\": {\n          \"description\": \"Jumlah tagihan jika tipe pengingat adalah 'BILL'. Null jika tidak ada atau tidak relevan.\",\n          \"type\": [\"number\", \"null\"],\n          \"format\": \"decimal\"\n        },\n        \"bill_currency\": {\n          \"description\": \"Mata uang tagihan (default 'IDR' jika tipe 'BILL'). Null jika tipe bukan 'BILL' atau tidak relevan.\",\n          \"type\": [\"string\", \"null\"],\n          \"default\": \"IDR\"\n        },\n        \"bill_due_day_of_month\": {\n          \"description\": \"Tanggal jatuh tempo bulanan (1-31) jika tipe 'BILL' dan recurrence_type 'MONTHLY_DATE'. Null jika tidak relevan.\",\n          \"type\": [\"integer\", \"null\"],\n          \"minimum\": 1,\n          \"maximum\": 31\n        }\n      },\n      \"required\": [\n        \"type\",\n        \"message\",\n        \"next_trigger_datetime\",\n        \"recurrence_type\",\n        \"recurrence_detail\",\n        \"bill_amount\",\n        \"bill_currency\",\n        \"bill_due_day_of_month\"\n      ]\n    },\n    \"clarification_info\": {\n      \"description\": \"Informasi terkait kebutuhan klarifikasi dari pengguna. Objek ini dan semua sub-fieldnya WAJIB ADA.\",\n      \"type\": \"object\",\n      \"properties\": {\n        \"is_needed\": {\n          \"description\": \"True jika AI membutuhkan klarifikasi lebih lanjut dari pengguna, false jika tidak.\",\n          \"type\": \"boolean\",\n          \"default\": false\n        },\n        \"question_for_user\": {\n          \"description\": \"Pertanyaan spesifik untuk pengguna jika is_needed adalah true. Null jika tidak.\",\n          \"type\": [\"string\", \"null\"]\n        }\n      },\n      \"required\": [\n        \"is_needed\",\n        \"question_for_user\"\n      ]\n    },\n    \"contextual_info_for_n8n\": {\n      \"description\": \"Informasi kontekstual tambahan untuk n8n guna memproses aksi tertentu. Objek ini dan semua sub-fieldnya WAJIB ADA.\",\n      \"type\": \"object\",\n      \"properties\": {\n        \"original_user_query_for_action\": {\n          \"description\": \"Deskripsi mentah dari pengguna yang digunakan untuk mencari/menghapus pengingat atau konteks aksi lainnya. Null jika tidak relevan.\",\n          \"type\": [\"string\", \"null\"]\n        },\n        \"bill_description_context\": {\n          \"description\": \"Konteks deskripsi tagihan yang disebutkan pengguna (e.g., 'tagihan wifi', 'listrik bulan kemarin'). Null jika tidak relevan.\",\n          \"type\": [\"string\", \"null\"]\n        }\n      },\n      \"required\": [\n        \"original_user_query_for_action\",\n        \"bill_description_context\"\n      ]\n    }\n  },\n  \"required\": [\n    \"action_type\",\n    \"response_to_user\",\n    \"reminder_details\",\n    \"clarification_info\",\n    \"contextual_info_for_n8n\"\n  ]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [520, 280], "id": "eed8b2ff-1522-44a1-adad-3ff6e8bc709e", "name": "Structured Output Parser"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.output.action_type }}", "rightValue": "=GENERAL_CHAT_RESPONSE", "operator": {"type": "string", "operation": "equals"}, "id": "8e374010-289a-44ac-87a6-d2de4493013f"}], "combinator": "and"}, "renameOutput": true, "outputKey": "GENERAL_CHAT_RESPONSE"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bcd7f286-974f-44b8-b312-115769ed7409", "leftValue": "={{ $json.output.action_type }}", "rightValue": "ASK_CLARIFICATION", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "ASK_CLARIFICATION"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "984e348e-e75a-487f-9a18-2c32c19c24b6", "leftValue": "={{ $json.output.action_type }}", "rightValue": "CREATE_REMINDER_CONFIRMATION", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CREATE_REMINDER_CONFIRMATION"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ebef06f6-75f8-4dec-a2fb-a8face84f7d8", "leftValue": "={{ $json.output.action_type }}", "rightValue": "ERROR_UNABLE_TO_PROCESS", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "ERROR_UNABLE_TO_PROCESS"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f1c55378-11eb-4e59-ab5c-5f4ff70ce971", "leftValue": "={{ $json.output.action_type }}", "rightValue": "LIST_REMINDERS_REQUEST", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "LIST_REMINDERS_REQUEST"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1b82af3d-1499-4c88-b3a5-36f04c79fb5e", "leftValue": "={{ $json.output.action_type }}", "rightValue": "DELETE_REMINDER_PROMPT", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DELETE_REMINDER_PROMPT"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7abe4ec1-0fb2-48be-8ab8-d65ef2089b88", "leftValue": "={{ $json.output.action_type }}", "rightValue": "CHANGE_DATA", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CHANGE_DATA"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6ce4ec0d-7773-4df3-9530-cdf933fd375b", "leftValue": "={{ $json.output.action_type }}", "rightValue": "CHANGE_TZ_DATA", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CHANGE_TZ_DATA"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "72352d1e-f79a-4ca6-8e54-52bd2be1eb0b", "leftValue": "={{ $json.output.action_type }}", "rightValue": "BUY_PLAN", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "BUY_PLAN"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [720, -66], "id": "b2a6daf2-5d20-4f0f-a501-d2c08885c639", "name": "Switch"}, {"parameters": {"operation": "upsert", "table": {"__rl": true, "value": "general_reminders", "mode": "list", "cachedResultName": "general_reminders"}, "dataMode": "defineBelow", "columnToMatchOn": "user_id", "valueToMatchOn": "={{ $('MySQL1').item.json.id }}", "valuesToSend": {"values": [{"column": "reminder_message", "value": "={{ $json.output.reminder_details.message }}"}, {"column": "original_input_text", "value": "={{ $json.output.contextual_info_for_n8n.original_user_query_for_action }}"}, {"column": "recurrence_type", "value": "={{ $json.output.reminder_details.recurrence_type }}"}, {"column": "next_trigger_datetime", "value": "={{ $json.output.reminder_details.next_trigger_datetime }}"}, {"column": "next_trigger_response", "value": "={{ $json.output.reminder_details.next_trigger_response }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [1160, -440], "id": "012bf38c-c486-44e7-8970-e415fae9e18c", "name": "MySQL", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.output.reminder_details.type }}", "rightValue": "GENERAL", "operator": {"type": "string", "operation": "equals"}, "id": "930d6493-4c7a-4cf5-aef4-957bd82da584"}], "combinator": "and"}, "renameOutput": true, "outputKey": "GENERAL"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8e3a3e3d-b433-4383-9436-659f6d4edd8a", "leftValue": "={{ $json.output.reminder_details.type }}", "rightValue": "BILL", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "BILL"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [940, -440], "id": "5fc316a0-366e-4ce7-b66f-fcb0c60e4ef7", "name": "Switch1"}, {"parameters": {"descriptionType": "manual", "toolDescription": "tool getReminders untuk mengambil data pengingat dari database", "operation": "select", "table": {"__rl": true, "value": "general_reminders", "mode": "list", "cachedResultName": "general_reminders"}, "where": {"values": [{"column": "user_id", "value": "={{ $('MySQL1').item.json.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySqlTool", "typeVersion": 2.4, "position": [160, 280], "id": "8cfdc4ea-8634-4741-9da1-8a744fc1ef15", "name": "get<PERSON><PERSON><PERSON>s", "alwaysOutputData": true, "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('MySQL1').item.json.whatsapp_number }}", "collectionName": "sipaling_chat_histories", "databaseName": "admin", "contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryMongoDbChat", "typeVersion": 1, "position": [-80, 280], "id": "a590d0ee-f508-47f3-8b27-746b171b7b21", "name": "MongoDB Chat Memory", "credentials": {"mongoDb": {"id": "h8HyolnIpo4PCHfv", "name": "MongoDB account"}}}, {"parameters": {"method": "POST", "url": "http://*************:5005/api/whatsapp/start-typing", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"chatId\": \"{{ $json.body.data.from }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1300, 60], "id": "5f89628e-2dff-42cd-bcbc-f435029b140a", "name": "StartTyping"}, {"parameters": {"operation": "select", "table": {"__rl": true, "value": "general_reminders", "mode": "list", "cachedResultName": "general_reminders"}, "where": {"values": [{"column": "user_id", "value": "={{ $('MySQL1').item.json.id }}"}, {"column": "reminder_message", "value": "={{ $json.output.reminder_details.message }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [940, 260], "id": "d359d524-952d-430d-93f5-292128b0e862", "name": "MySQL2", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "general_reminders", "mode": "list", "cachedResultName": "general_reminders"}, "dataMode": "defineBelow", "columnToMatchOn": "id", "valueToMatchOn": "={{ $json.id }}", "valuesToSend": {"values": [{"column": "next_trigger_datetime", "value": "={{ $('Switch').item.json.output.reminder_details.next_trigger_datetime }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [1160, 260], "id": "1eb02f0f-19c1-4523-9bc6-b1325d26a8ee", "name": "MySQL3", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"method": "POST", "url": "=http://*************:5005/api/whatsapp/send-reply", "sendBody": true, "bodyParameters": {"parameters": [{"name": "messageId", "value": "={{ $('Webhook').first().json.body.data.messageId }}"}, {"name": "=message", "value": "={{ $('Switch').item.json.output.response_to_user }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1380, 260], "id": "f60ff50a-9cb3-40b9-a6a0-803ecbee8c53", "name": "HTTP Request1", "retryOnFail": true}, {"parameters": {"operation": "select", "table": {"__rl": true, "value": "general_reminders", "mode": "list", "cachedResultName": "general_reminders"}, "where": {"values": [{"column": "user_id", "value": "={{ $('MySQL1').item.json.id }}"}, {"column": "reminder_message", "value": "={{ $json.output.reminder_details.message }}"}, {"column": "next_trigger_datetime", "value": "={{ $json.output.reminder_details.next_trigger_datetime }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [940, 60], "id": "2eb31609-0336-4c55-a75e-02f285c6cdaf", "name": "MySQL4", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"operation": "deleteTable", "table": {"__rl": true, "value": "general_reminders", "mode": "list", "cachedResultName": "general_reminders"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.id }}"}, {"column": "reminder_message", "value": "={{ $json.reminder_message }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [1160, 60], "id": "3e2e7a9a-d6e9-45d5-813c-c1915fa96959", "name": "MySQL5", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"method": "POST", "url": "=http://*************:5005/api/whatsapp/send-reply", "sendBody": true, "bodyParameters": {"parameters": [{"name": "messageId", "value": "={{ $('Webhook').first().json.body.data.messageId }}"}, {"name": "=message", "value": "={{ $('Switch').item.json.output.response_to_user }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1380, 60], "id": "7a92d500-f1a0-497f-b308-135a1a1ea58c", "name": "HTTP Request2", "retryOnFail": true}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "dataMode": "defineBelow", "columnToMatchOn": "id", "valueToMatchOn": "={{ $('MySQL1').item.json.id }}", "valuesToSend": {"values": [{"column": "timezone", "value": "={{ $('AI Agent').item.json.output.reminder_details.message }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [940, 860], "id": "2fac36b0-11f1-493d-adc8-7395c044b77e", "name": "MySQL7", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"method": "POST", "url": "=http://*************:5005/api/whatsapp/send-reply", "sendBody": true, "bodyParameters": {"parameters": [{"name": "messageId", "value": "={{ $('Webhook').first().json.body.data.messageId }}"}, {"name": "=message", "value": "={{ $('Switch').item.json.output.response_to_user }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, 860], "id": "8994dc5e-51df-461f-9878-6898d9b666be", "name": "HTTP Request3", "retryOnFail": true}, {"parameters": {"operation": "delete", "collection": "sipaling_chat_histories", "query": "={\n  \"sessionId\": \"{{ $('MySQL1').item.json.whatsapp_number }}\"\n}"}, "type": "n8n-nodes-base.mongoDb", "typeVersion": 1.1, "position": [940, -140], "id": "b10d2d31-2f55-43b5-9576-d426cd0390fe", "name": "MongoDB", "credentials": {"mongoDb": {"id": "h8HyolnIpo4PCHfv", "name": "MongoDB account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "tool getAllPlan untuk mengambil data semua plan dari database", "operation": "select", "table": {"__rl": true, "value": "plans", "mode": "list", "cachedResultName": "plans"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.mySqlTool", "typeVersion": 2.4, "position": [280, 280], "id": "f1bd6830-60c4-451e-baa6-d9d78854d34f", "name": "getAllPlan", "alwaysOutputData": true, "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"method": "POST", "url": "=http://*************:5005/api/whatsapp/send-reply", "sendBody": true, "bodyParameters": {"parameters": [{"name": "messageId", "value": "={{ $('Webhook').first().json.body.data.messageId }}"}, {"name": "=message", "value": "={{ $('Switch2').item.json.output.response_to_user }}\n\n{{ $json.payment_url }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1380, 560], "id": "dc98787a-e8bf-4da6-9de5-0cef9e441a31", "name": "HTTP Request4", "retryOnFail": true}, {"parameters": {"method": "POST", "url": " https://api.midtrans.com/v1/payment-links", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "Basic TWlkLXNlcnZlci03UU45cm5KVk5nOUFob1ZWQ3FDSF9fYzQ6"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"transaction_details\": {\n    \"order_id\": \"{{ $json.output.reminder_details.message }}-{{ $('MySQL1').item.json.whatsapp_number }}-{{ $('Webhook').item.json.body.timestamp.split(\":\")[2] }}\",\n    \"gross_amount\": 9970\n  },\n  \"callbacks\": {\n    \"finish\": \"http://wa.me/{{ $('Webhook').item.json.body.data.to.split(\"@\")[0] }}?text=halo\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, 460], "id": "cd674462-e2a8-40a6-b7ef-0dd4355ceb57", "name": "HTTP Request5"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.output.reminder_details.message }}", "rightValue": "premium", "operator": {"type": "string", "operation": "equals"}, "id": "d6809920-fecc-46f8-b06c-6e0184f16674"}], "combinator": "and"}, "renameOutput": true, "outputKey": "premium"}, {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5cfd4f9c-e3ec-4c4d-81c4-7eee6786905f", "leftValue": "={{ $json.output.reminder_details.message }}", "rightValue": "ingatanSuper", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "ingatanSuper"}]}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [940, 560], "id": "c7796233-2713-44fa-9a3e-56ea43aea584", "name": "Switch2"}, {"parameters": {"method": "POST", "url": "https://api.midtrans.com/v1/payment-links", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "Basic TWlkLXNlcnZlci03UU45cm5KVk5nOUFob1ZWQ3FDSF9fYzQ6"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"transaction_details\": {\n    \"order_id\": \"{{ $json.output.reminder_details.message }}-{{ $('MySQL1').item.json.whatsapp_number }}\",\n    \"gross_amount\": 30110\n  },\n  \"usage_limit\": 2\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, 660], "id": "bfca6c85-dbca-49c0-9b0f-c1b8ee42db60", "name": "HTTP Request6"}, {"parameters": {"httpMethod": "POST", "path": "midtrans", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1540, 580], "id": "b806f801-5bbf-4b3e-ba5c-2cd58875f7a4", "name": "NotificationPay", "webhookId": "bc01306f-146d-4754-9908-86abc40146b0"}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a86c582e-97c9-4297-a4fd-7fd84f2cdf44", "leftValue": "={{ $json.body.transaction_status }}", "rightValue": "settlement", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1320, 580], "id": "331b1717-b238-49be-9175-61ea74ce430c", "name": "If1"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "dataMode": "defineBelow", "columnToMatchOn": "whatsapp_number", "valueToMatchOn": "={{ $json.body.order_id.split(\"-\")[1] }}", "valuesToSend": {"values": [{"column": "plan_id", "value": "2"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [-880, 580], "id": "3dbabb34-9198-4d5e-ae1a-8194728159f8", "name": "MySQL6", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"method": "POST", "url": "=http://*************:5005/api/whatsapp/send-media", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('NotificationPay').item.json.body.order_id.split(\"-\")[1] }}"}, {"parameterType": "formBinaryData", "name": "media", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-660, 380], "id": "d8701302-3a31-4576-b976-0f03a715ef34", "name": "HTTP Request7", "retryOnFail": true}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {"mimeType": "image/png"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-880, 380], "id": "e5fa1b17-9f2c-42be-a1f6-57fb31d60dce", "name": "Convert to File"}, {"parameters": {"assignments": {"assignments": [{"id": "3cf9c78c-7b1c-46bf-a131-1769a0b3d538", "name": "base64", "value": "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**************************************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", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1100, 380], "id": "93be4524-4c20-4eb3-b62c-d152a2394f99", "name": "<PERSON>"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.body.order_id.split(\"-\")[0] }}", "rightValue": "premium", "operator": {"type": "string", "operation": "equals"}, "id": "6e1ea632-d8f4-465c-aab4-869ffb7b278b"}], "combinator": "and"}, "renameOutput": true, "outputKey": "premium"}, {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "17fc231d-1922-490e-81d5-1d19f2803c23", "leftValue": "={{ $json.body.order_id.split(\"-\")[0] }}", "rightValue": "ingatanSuper", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "ingatanSuper"}]}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1100, 680], "id": "a3367b2d-21f4-4914-89e8-e3cc4d08c0ce", "name": "Switch3"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "dataMode": "defineBelow", "columnToMatchOn": "whatsapp_number", "valueToMatchOn": "={{ $json.body.order_id.split(\"-\")[1] }}", "valuesToSend": {"values": [{"column": "plan_id", "value": "3"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [-880, 780], "id": "06df7cd7-a976-40fa-8413-4b1889da1eaa", "name": "MySQL8", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"table": {"__rl": true, "value": "user_subscriptions", "mode": "list", "cachedResultName": "user_subscriptions"}, "dataMode": "defineBelow", "valuesToSend": {"values": [{"column": "plan_id", "value": "={{ $('MySQL10').item.json.plan_id }}"}, {"column": "start_date", "value": "={{ $json.startDate }}"}, {"column": "end_date", "value": "={{ $json.endDate }}"}, {"column": "status", "value": "active"}, {"column": "payment_gateway_transaction_id", "value": "={{ $('NotificationPay').item.json.body.transaction_id }}"}, {"column": "amount_paid", "value": "={{ $('NotificationPay').item.json.body.gross_amount }}"}, {"column": "user_id", "value": "={{ $('MySQL10').item.json.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [-220, 680], "id": "15ad38d4-dbae-4f44-b285-d22d545e5530", "name": "MySQL9", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"operation": "select", "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "returnAll": true, "where": {"values": [{"column": "whatsapp_number", "value": "={{ $('NotificationPay').first().json.body.order_id.split(\"-\")[1] }}"}]}, "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [-660, 680], "id": "9b12d32d-94a0-4126-aeeb-d01e4b651ff3", "name": "MySQL10", "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}, {"parameters": {"jsCode": "const now = Date.now(); // dalam milidetik\nconst plus30Days = now + (30 * 24 * 60 * 60 * 1000); // tambah 30 hari\nconst start = Math.floor(Date.now() / 1000);\n\nreturn {\n  json: {\n    startDate: start,\n    endDate: Math.floor(plus30Days / 1000) // hasil dalam detik\n  }\n};\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-440, 680], "id": "823d7507-14eb-416c-82c8-5dd167563629", "name": "Code"}, {"parameters": {"descriptionType": "manual", "toolDescription": "tool getSubscription untuk mengambil data Subscription dari database", "operation": "select", "table": {"__rl": true, "value": "user_subscriptions", "mode": "list", "cachedResultName": "user_subscriptions"}, "where": {"values": [{"column": "user_id", "value": "={{ $('MySQL1').item.json.id }}"}]}, "sort": {"values": [{"column": "end_date"}]}, "options": {}}, "type": "n8n-nodes-base.mySqlTool", "typeVersion": 2.4, "position": [400, 280], "id": "ea48898f-d220-42f6-a670-8043f7af2e04", "name": "getSubscription", "alwaysOutputData": true, "credentials": {"mySql": {"id": "KEcGUcJiBWRFOz3v", "name": "MySQL SiPaling"}}}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "StartTyping", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "If": {"main": [[{"node": "MySQL1", "type": "main", "index": 0}], [{"node": "AddUser", "type": "main", "index": 0}]]}, "CheckUser": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "AddUser": {"main": [[{"node": "MySQL1", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "MySQL1": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "getPlan": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Switch": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "Switch1", "type": "main", "index": 0}, {"node": "MongoDB", "type": "main", "index": 0}], [{"node": "HTTP Request", "type": "main", "index": 0}, {"node": "MongoDB", "type": "main", "index": 0}], [{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "MySQL4", "type": "main", "index": 0}], [{"node": "MySQL2", "type": "main", "index": 0}], [{"node": "MySQL7", "type": "main", "index": 0}], [{"node": "MongoDB", "type": "main", "index": 0}, {"node": "Switch2", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "MySQL", "type": "main", "index": 0}], []]}, "MySQL": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "getReminders": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "MongoDB Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "StartTyping": {"main": [[{"node": "CheckUser", "type": "main", "index": 0}]]}, "MySQL2": {"main": [[{"node": "MySQL3", "type": "main", "index": 0}]]}, "MySQL3": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "MySQL4": {"main": [[{"node": "MySQL5", "type": "main", "index": 0}]]}, "MySQL5": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "MySQL7": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}, "getAllPlan": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "HTTP Request5": {"main": [[{"node": "HTTP Request4", "type": "main", "index": 0}]]}, "Switch2": {"main": [[{"node": "HTTP Request5", "type": "main", "index": 0}], [{"node": "HTTP Request6", "type": "main", "index": 0}]]}, "HTTP Request6": {"main": [[{"node": "HTTP Request4", "type": "main", "index": 0}]]}, "NotificationPay": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Switch3", "type": "main", "index": 0}], []]}, "HTTP Request7": {"main": [[]]}, "Convert to File": {"main": [[{"node": "HTTP Request7", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Switch3": {"main": [[{"node": "MySQL6", "type": "main", "index": 0}], [{"node": "MySQL8", "type": "main", "index": 0}]]}, "MySQL10": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "MySQL6": {"main": [[{"node": "MySQL10", "type": "main", "index": 0}]]}, "MySQL8": {"main": [[{"node": "MySQL10", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "MySQL9", "type": "main", "index": 0}]]}, "getSubscription": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "b956ee64-09c6-4ba4-a8ec-1aeb13769489", "meta": {"templateCredsSetupCompleted": true, "instanceId": "edd8393f0e8a0c684baf1ed5d2c51210193cb065717518e62c2dc8bac1cc2313"}, "id": "J3dUTZ3crFH8zV9O", "tags": []}